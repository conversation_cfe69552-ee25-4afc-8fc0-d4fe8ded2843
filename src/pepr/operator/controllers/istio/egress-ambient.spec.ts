/**
 * Copyright 2024 Defense Unicorns
 * SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial
 */

import { kind } from "pepr";
import {
  afterEach,
  beforeEach,
  describe,
  expect,
  it,
  MockedFunction,
  vi,
} from "vitest";
import { Direction, IstioGateway, RemoteProtocol } from "../../crd";
import { defaultEgressMocks, updateEgressMocks } from "./defaultTestMocks";
import { PackageHostMap } from "./types";

import {
  applyAmbientEgressResources,
  createAmbientWorkloadEgressResources,
  purgeAmbientEgressResources,
} from "./egress-ambient";

// Mock purge orphans
const mockPurgeOrphans: MockedFunction<() => Promise<void>> = vi.fn();
vi.mock("../utils", async () => {
  const originalModule = (await vi.importActual("../utils")) as object;
  return {
    ...originalModule,
    purgeOrphans: vi.fn(async <T>(fn: () => Promise<T>) => fn()),
  };
});

// Mock pepr functions
vi.mock("pepr", () => ({
  K8s: vi.fn(),
  Log: {
    child: vi.fn(() => ({
      info: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      level: "info",
    })),
  },
  kind: {
    Gateway: "Gateway",
    VirtualService: "VirtualService",
    ServiceEntry: "ServiceEntry",
    Namespace: "Namespace",
    Service: "Service",
    ServiceAccount: "ServiceAccount",
    Waypoint: "Waypoint",
  },
}));

describe("test applyAmbientEgressResources", () => {
  beforeEach(async () => {
    process.env.PEPR_WATCH_MODE = "true";
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  it("should apply ambient egress resources", async () => {
    updateEgressMocks(defaultEgressMocks);

    await applyAmbientEgressResources(["test-package-1", "test-package-2"], 1);

    expect(defaultEgressMocks.applyWaypointMock).toHaveBeenCalledTimes(1);
  });

  it("should handle empty package list", async () => {
    updateEgressMocks(defaultEgressMocks);

    await expect(applyAmbientEgressResources([], 1)).resolves.not.toThrow();

    // No resources should be applied for empty list
    expect(defaultEgressMocks.applyWaypointMock).not.toHaveBeenCalled();
  });
});

describe("test purgeAmbientEgressResources", () => {
  beforeEach(() => {
    process.env.PEPR_WATCH_MODE = "true";
    vi.useFakeTimers();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  it("should purge ambient egress resources", async () => {
    updateEgressMocks(defaultEgressMocks);
  });

});

describe("test createAmbientWorkloadEgressResources", () => {
  beforeEach(() => {
    process.env.PEPR_WATCH_MODE = "true";
    vi.useFakeTimers();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  it("should create ambient egress resources", async () => {
    updateEgressMocks(defaultEgressMocks);

    await createAmbientWorkloadEgressResources(
      {
        "example.com": {
          portProtocol: [{ port: 443, protocol: RemoteProtocol.TLS }],
        },
      },
      [
        {
          direction: Direction.Egress,
          remoteHost: "example.com",
          remoteProtocol: RemoteProtocol.TLS,
          port: 443,
          serviceAccount: "test-sa",
        },
      ],
      "test-package",
      "test-ns",
      "1",
      [],
    );

    expect(defaultEgressMocks.applySeMock).toHaveBeenCalledTimes(1);
    expect(defaultEgressMocks.applyApMock).toHaveBeenCalledTimes(1);
  });

  it("should create ambient egress resources for multiple hosts", async () => {
    updateEgressMocks(defaultEgressMocks);

    await createAmbientWorkloadEgressResources(
      {
        "example.com": {
          portProtocol: [{ port: 443, protocol: RemoteProtocol.TLS }],
        },
        "httpbin.org": {
          portProtocol: [{ port: 443, protocol: RemoteProtocol.TLS }],
        },
      },
      [
        {
          direction: Direction.Egress,
          remoteHost: "example.com",
          remoteProtocol: RemoteProtocol.TLS,
          port: 443,
          serviceAccount: "test-sa",
        },
        {
          direction: Direction.Egress,
          remoteHost: "httpbin.org",
          remoteProtocol: RemoteProtocol.TLS,
          port: 443,
          serviceAccount: "test-sa",
        },
      ],
      "test-package",
      "test-ns",
      "1",
      [],
    );

    expect(defaultEgressMocks.applySeMock).toHaveBeenCalledTimes(2);
    expect(defaultEgressMocks.applyApMock).toHaveBeenCalledTimes(2);
  });

  it("should create ambient egress resources, no service account", async () => {
    updateEgressMocks(defaultEgressMocks);

    await createAmbientWorkloadEgressResources(
      {
        "example.com": {
          portProtocol: [{ port: 443, protocol: RemoteProtocol.TLS }],
        },
      },
      [
        {
          direction: Direction.Egress,
          remoteHost: "example.com",
          remoteProtocol: RemoteProtocol.TLS,
          port: 443,
        },
      ],
      "test-package",
      "test-ns",
      "1",
      [],
    );

    expect(defaultEgressMocks.applySeMock).toHaveBeenCalledTimes(1);
    expect(defaultEgressMocks.applyApMock).not.toHaveBeenCalled();
  });

  it("should err if bad service account specified", async () => {
    // Mock ServiceAccount get inNamespace error
    const errorMessage = "ServiceAccount foo does not exist in namespace test-ns. Please create the ServiceAccount and retry.";
    const getServiceAccountMock = vi
      .fn<() => Promise<kind.ServiceAccount>>()
      .mockRejectedValue(new Error("Service account not found"));

    updateEgressMocks({
      ...defaultEgressMocks,
      getServiceAccountMock,
    });

    // Should throw error
    await expect(
      createAmbientWorkloadEgressResources(
        {
          "example.com": {
            portProtocol: [{ port: 443, protocol: RemoteProtocol.TLS }],
          },
        },
        [
          {
            direction: Direction.Egress,
            remoteHost: "example.com",
            remoteProtocol: RemoteProtocol.TLS,
            port: 443,
            serviceAccount: "foo",
          },
        ],
        "test-package",
        "test-ns",
        "1",
        [],
      ),
    ).rejects.toThrow(errorMessage);
  });

});